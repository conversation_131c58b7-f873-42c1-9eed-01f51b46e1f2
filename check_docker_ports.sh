#!/bin/bash

# Docker Port Allocation Checker
# This script checks which ports are allocated by Docker containers

echo "=== Docker Port Allocation Report ==="
echo "Generated on: $(date)"
echo ""

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "Error: Docker is not running or not accessible"
    exit 1
fi

echo "1. RUNNING CONTAINERS WITH PORT MAPPINGS:"
echo "=========================================="
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Ports}}\t{{.Status}}" | grep -E "(PORTS|:)"

echo ""
echo "2. ALL CONTAINERS (including stopped) WITH PORT MAPPINGS:"
echo "========================================================="
docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Ports}}\t{{.Status}}" | grep -E "(PORTS|:)"

echo ""
echo "3. DETAILED PORT INFORMATION:"
echo "============================="
for container in $(docker ps -q); do
    name=$(docker inspect --format='{{.Name}}' $container | sed 's/\///')
    echo "Container: $name"
    docker port $container 2>/dev/null || echo "  No port mappings"
    echo ""
done

echo ""
echo "4. DOCKER NETWORKS AND THEIR SUBNETS:"
echo "====================================="
docker network ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"
echo ""
for network in $(docker network ls --format "{{.Name}}" | grep -v NETWORK); do
    echo "Network: $network"
    docker network inspect $network --format "  Subnet: {{range .IPAM.Config}}{{.Subnet}}{{end}}" 2>/dev/null
    echo ""
done

echo ""
echo "5. DOCKER COMPOSE SERVICES (if any):"
echo "===================================="
if command -v docker-compose >/dev/null 2>&1; then
    docker-compose ps 2>/dev/null || echo "No docker-compose services found or not in a compose directory"
elif command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
    docker compose ps 2>/dev/null || echo "No docker compose services found or not in a compose directory"
else
    echo "Docker Compose not available"
fi

echo ""
echo "6. SYSTEM PORTS IN USE (for reference):"
echo "======================================="
echo "Listening TCP ports:"
netstat -tlnp 2>/dev/null | grep LISTEN | head -20 || ss -tlnp | grep LISTEN | head -20

echo ""
echo "=== End of Report ==="
